from src import utils
import src.LangGraphUtils as LangGraphUtils


from dotenv import load_dotenv
import os

load_dotenv()


engine = utils.get_engine()


#with ls.trace("prompts-pipeline") as rt:
print("starting")
prompts = utils.load_prompt_sequence("/Users/<USER>/Documents/GitHub/prompts-library/prompts/hq.yaml")
#markdown = utils.load_markdown("/Users/<USER>/Documents/GitHub/prompts-library/markdowns/acconsis.de.md") #utils.load_markdown_db("dornbach.de", "wget_markdown")

markdown = utils.load_markdown_db("1atreuhand.ch", "wget2_runs", engine)

workflow = utils.create_workflow_state(prompts)
lg = LangGraphUtils.LangGraphUtils(model="gpt-5-nano-2025-08-07")
#lg = LangGraphUtils.LangGraphUtils(model="gpt-5-nano-2025-08-07")
graph = lg.build_graph(prompts, workflow)

print("initial markdown length: ", len(markdown))
markdown = utils.clean_markdown(markdown)
print("cleaned markdown length: ", len(markdown))

print("markdown: ", markdown[0:100])

print("Running graph")
initial_state = {"markdown": markdown[0:350_000]}
state_stream = graph.stream(initial_state)

print("updates")
for update in state_stream:
    print(update)
