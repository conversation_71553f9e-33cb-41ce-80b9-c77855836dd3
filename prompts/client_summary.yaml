- name: find_quote
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    
    You are given a company's website content in a markdown chunk below. 
    Task: Extract sentences or list fragments that explicitly describe the types of clients or audiences the company serves (who buys/uses the offering).
    - Prefer mentions headed by organization/person nouns (e.g., companies, agencies, municipalities, hospitals, universities, utilities, contractors, OEMs, manufacturers, suppliers, distributors, labs, owners, developers).
    - Accept sector phrases only if they are explicitly framed as clients (e.g., “automotive companies”, “aerospace manufacturers”). Bare sector words alone (e.g., "automotive", "aerospace") should be ignored unless paired with a client noun.
    - Exclude services, markets/industries without a client head noun, geographic coverage, testimonials, careers, values, and marketing fluff.
    - If multiple relevant fragments appear in this chunk, concatenate them using three dots (...) between fragments.
    - Return at most 600 characters, preserving original wording.
    - If no client descriptions are present in this chunk, return exactly: Unknown

    Output: the extracted client-type text (<=600 chars) or the single word Unknown in a JSON field named "quote".

    Respond with a JSON object like:

    {{
      "quote": ... // extracted client-type text (<=600 chars) or the single word Unknown
    }}
    
    But don't preface it with ```json.

    <markdown>
    {markdown}
    </markdown>

- name: summarize_clients
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    You are given a brief client-focused quote in the input below.

    Task: produce a concise, comma-separated list of client categories (who buys/uses). Rules:
    - Use plural organization/person categories only (e.g., government agencies, municipalities, utilities, manufacturers, OEMs, contractors, hospitals, universities, labs, property owners, developers, schools).
    - Do not list services, facilities/asset types (e.g., residential buildings), vague terms (e.g., "industry"), or internal roles (e.g., employees).
    - Convert bare sector words to client-headed nouns only if the quote already implies clients (e.g., “automotive” → “automotive companies” is allowed; “aerospace” → “aerospace manufacturers” only if manufacturers are mentioned).
    - Maximum length: 75 characters total.
    - No period or hyphen at the end. No the word "and". Do not cut words mid-way.
    - If the quote is Unknown or lacks explicit client categories, return an empty list.

    Output: JSON with a field "categories" with a list of comma-separated client categories (≤75 chars) or an empty list.

    Respond with a JSON object like:
    {{
      "categories": []
    }}

    But don't preface it with ```json.

    Input:
    - input: {find_quote[quote]}

- name: translate_and_clean
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    In the input below you are given a comma-separated list that should describe client categories.
    Task:
    - If the text is not in English, translate it into English first.
    - Normalize into plural organization/person client categories only (who buys/uses):
      -- Keep nouns headed by organizations/people: companies/firms/businesses, agencies, municipalities, governments, hospitals, clinics, universities, schools, utilities, OEMs, manufacturers, suppliers, distributors, contractors, architects, engineers, developers, property owners, labs, NDT service providers, public authorities, NGOs.
      -- Drop facilities/asset types (e.g., residential buildings, commercial properties, industrial facilities).
      -- Drop vague sector-only items (e.g., "industry", "aerospace") unless converted to a client-headed noun (e.g., "aerospace manufacturers"). If a sector is clearly intended as a client but lacks a head noun, convert to “<sector> companies”.
      -- Drop internal roles and generic placeholders (e.g., employees, users, stakeholders).
    - Remove duplicates and near-duplicates.
    - If the last item is cut off (mid-word, trailing hyphen, truncated letters, or ends with "and"), remove that last item completely.
    - Remove trailing commas/spaces. Do not add new sectors or invent client types beyond what is implied by the text.
    - If the input list is empty, return an empty list.

    Output rules:
    - Return only the cleaned list in English.
    - Ensure formatting: each item separated by a comma + one space (", ").
    - No missing or extra spaces, no double commas, no trailing comma.
    - Output must be a single line of comma-separated items, or an empty list.

    Respond with a JSON object like:
    {{
      "clients_final": [] // list of cleaned client categories or an empty list
      "original_input": "..." // the original input text
    }}

    But don't preface it with ```json.

    Input:
    - input: {summarize_clients[categories]}