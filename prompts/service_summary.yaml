- name: find_services_raw
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    
    You are given a company's website content in a markdown chunk below. 
    Task: extract sentences or list fragments that explicitly describe the services or solutions the company provides.
    - Focus on activity- or category-level descriptions (what the company offers or delivers).
    - Exclude clients, testimonials, history, leadership bios, careers, sustainability statements, geographic coverage, and marketing fluff.
    - Do not infer missing services; use only explicit text.
    - If multiple relevant fragments appear in this chunk, concatenate them using three dots (...) between fragments.
    - Return at most 600 characters, preserving original wording.
    - If no service description is present, return exactly: Unknown

    Output: the extracted service-only text (<=600 chars) or the single word Unknown in a JSON field named "quote".

    Respond with a JSON object like:

    {{
      "quote": ... // extracted client-type text (<=600 chars) or the single word Unknown
    }}
    
    But don't preface it with ```json.

    <markdown>
    {markdown}
    </markdown>

- name: review_quote
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    Below in the input you are given a short quote that should describe company services.
    
    Task: refine the quote to keep only clear service descriptions.
    - Remove any non-service material (clients, locations, awards, history, values, marketing).
    - Keep concise category/offer wording only.
    - Do not add or infer content not present in the quote.
    - Return at most 300 characters.
    - If the quote does not clearly describe services, return exactly: Unknown
    
    Output: a trimmed service-only quote (<=300 chars) or the single word Unknown

    Respond with a JSON object like:
    {{
      "trimmed_quote": ... // trimmed service-only quote (<=300 chars) or the single word Unknown
    }}

    But don't preface it with ```json.

    Input:
    - input: {find_services_raw[quote]}

- name: summarize_services
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    

    You are given a brief service-focused quote in the input below.
    
    Task: produce a concise, comma-separated list of service categories.
    Rules:
    - Use plain English category labels only.
    - No client types, no regions, no history, no marketing language.
    - Do not infer services not explicitly present.
    - Maximum length: 75 characters total.
    - Do not end with a period or hyphen.
    - Do not include the word "and".
    - Do not cut words mid-way.
    - If the quote is Unknown or lacks explicit services, return an empty list [].
    
    Output: JSON with  a key "services" and a value that is a comma-separated service categories list (≤75 chars) or an empty list.

    Respond with a JSON object like:
    {{
      "services": [] // list of cleaned service categories or an empty list
    }}

    But don't preface it with ```json.

    Input:
    - input: {review_quote[trimmed_quote]}