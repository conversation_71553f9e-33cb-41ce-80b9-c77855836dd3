- name: test_step1
  prompt: |
    You are given markdown of a company website section.

    input: {markdown}

    Use Python (execute_python_code) to count the length of the characters in the input and print them.
    Make sure to assign the result to a variable so it can be captured.
    Reminder for gpt-4o-mini: NEVER paste the markdown, always use read_blob_text()

    Example code:
    ```python
    text = read_blob_text()
    length = len(text)
    print("Character count:" + str(length))
    ```

    Output: a JSON with the value of the variable "length":
    {{
      "length": ...
    }}
      But don't preface it with ```json.

- name: test_step2
  prompt: |
    You are given a number, use Python (execute_python_code) to divide it by 2.

    input: {test_step1[length]}

    Make sure to assign the result to a variable so it can be captured.
    Reminder for gpt-4o-mini: NEVER paste the markdown, always use read_blob_text()

    Example code:
    ```python
      x = "the input number"
      y = x / 2
      print("New number: " + str(y))
    ```

    Output: a JSON with the value of the variable "y".
    {{
      "y": ...
    }}
      But don't preface it with ```json.