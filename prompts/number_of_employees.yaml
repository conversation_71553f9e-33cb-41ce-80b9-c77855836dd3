- name: find_quote
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    From the following company description written in markdown, extract 1-2 sentences where number of employees is mentioned. If you don't find such sentence, return 'Unknown'. Don't add any comments.

    Keywords: employees, team members, workforce, staff, personnel, professionals, experts or similar. Also look for these keywords in other languages, such as Fachleuten, Mitarbeiter, Fachkräfte, Steuerberater, Köpfe etc. 

    Respond with a JSON object like:

    {{
      "employees_quote": ... // string, the quote text. "Unknown" if no quote is found.
    }}
    
    But don't preface it with ```json.

    <markdown>
    {markdown}
    </markdown>

- name: extract_number
  prompt: |
    For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
    
    From the following sentence, extract the number of employees. 
    - If range, take average (e.g., 50-100 → 75).  
    - If "more than X", "at least X", "mehr als X" or "über X", return X.
    - Output just the single number that is the best fit, or null if unsure. Don't output any additional text or comments.

    Respond with a JSON object like:
    {{
      "num_employees": int|null   //number of employees
    }}

    But don't preface it with ```json.

    Input:
    - sentence: {find_quote[employees_quote]}